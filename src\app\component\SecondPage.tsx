import React from "react";
import Image from "next/image";
import SpotlightCard from "../ui/SpotlightCard";

const SecondPage: React.FC = () => {
  return (
    <div
      id="about"
      className="min-h-screen relative overflow-hidden animated-gradient-bg"
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50 z-[1]"></div>

      {/* Background patterns */}
      <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] z-[1]"></div>
      <div className="absolute inset-0 bg-[conic-gradient(at_top_left,#45d6e9_10%,transparent_30%,transparent_70%,#45d6e9_90%)] opacity-[0.15] [background-size:30px_30px] scale-[1.5] mix-blend-overlay z-[1]"></div>

      {/* Mobile Layout (sm only) */}
      <section className="sm:hidden py-16 relative z-[2] px-4">
        <h1 className="text-4xl font-bold text-center text-[#45d6e9] mb-12 custom">
          About Me
        </h1>

        <div className="space-y-8">
          {/* Mobile Profile Picture */}
          <div className="relative w-full aspect-square max-w-[300px] mx-auto">
            {" "}
            {/* Reduced from 350px to 300px */}
            <div className="w-full h-full rounded-3xl overflow-hidden bg-[#1a1a1a] shadow-2xl shadow-[#45d6e9]/20">
              <Image
                src="/asset/me1.png"
                alt="Profile"
                fill
                sizes="(max-width: 380px) 300px, 100vw"
                className="object-cover object-top rounded-3xl"
                priority
              />
            </div>
            {/* Experience Badge - Adjusted size for mobile */}
            <div
              className="absolute -bottom-8 -right-5 w-[140px]  h-[90px] bg-black/80 backdrop-blur-sm
                        rounded-2xl border border-[#45d6e9]/20 flex flex-col items-center justify-center custom"
            >
              <h3 className="text-[#45d6e9] text-3xl font-bold custom">1</h3>
              <p className="text-gray-400 text-sm text-center leading-tight mt-1 raleway-font">
                Years
                <br />
                Experience
              </p>
            </div>
          </div>

          {/* Mobile Content */}
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-[#45d6e9]">
              Full Stack Web Developer
            </h2>
            <p className="text-gray-300 text-base leading-relaxed">
              Hey, my name is{" "}
              <span className="text-[#45d6e9] font-medium custom">Keshav</span>,
              a dedicated MERN Developer with a passion for building intuitive
              and responsive web applications. I enjoy crafting clean, efficient
              code that transforms ideas into interactive digital experiences.
            </p>

            {/* Mobile Skills Grid */}
            <div className="grid grid-cols-2 gap-3">
              {[
                { name: "MongoDB", level: "Intermediate Level" },
                { name: "Express", level: "Intermediate Level" },
                { name: "React", level: "Intermediate Level" },
                { name: "Node.js", level: "Intermediate Level" },
              ].map((skill) => (
                <div
                  key={skill.name}
                  className="bg-black/60 backdrop-blur-sm rounded-xl p-3
                            border border-[#45d6e9]/20"
                >
                  <h3 className="text-[#45d6e9] text-lg font-medium mb-1">
                    {skill.name}
                  </h3>
                  <p className="text-gray-400 text-xs">{skill.level}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Tablet Layout (sm to lg) */}
      <section className="hidden sm:block lg:hidden py-16 relative z-[2] px-6">
        <h1 className="text-5xl font-bold text-center text-[#45d6e9] mb-16">
          About Me
        </h1>

        <div className="max-w-2xl mx-auto space-y-12">
          {/* Profile Picture Container */}
          <div className="relative w-full aspect-[16/9] rounded-3xl overflow-visible">
            <div className="w-full h-full rounded-3xl overflow-hidden">
              <Image
                src="/asset/me1.png"
                alt="Profile"
                fill
                sizes="(min-width: 640px) 768px, 100vw"
                className="object-cover object-top rounded-3xl"
                priority
              />
            </div>

            <div
              className="absolute -bottom-8 -right-8 w-[160px] h-[90px] bg-black/80 backdrop-blur-sm
                        rounded-2xl border border-[#45d6e9]/20 flex flex-col items-center justify-center"
            >
              <h3 className="text-[#45d6e9] text-3xl font-bold">1+</h3>
              <p className="text-gray-400 text-sm text-center leading-tight mt-1">
                Years
                <br />
                Experience
              </p>
            </div>
          </div>

          {/* Content Section */}
          <div className="space-y-8">
            <h2 className="text-4xl font-bold text-[#45d6e9]">
              Full Stack Web Developer
            </h2>

            <p className="text-gray-300 text-lg leading-relaxed">
              Hey, my name is{" "}
              <span className="text-[#45d6e9] font-medium">Keshav</span>, a
              dedicated MERN Developer with a passion for building intuitive and
              responsive web applications. I enjoy crafting clean, efficient
              code that transforms ideas into interactive digital experiences.
            </p>

            {/* Skills Grid */}
            <div className="grid grid-cols-2 gap-6">
              {[
                { name: "MongoDB", level: "Intermediate Level" },
                { name: "Express", level: "Intermediate Level" },
                { name: "React", level: "Intermediate Level" },
                { name: "Node.js", level: "Intermediate Level" },
              ].map((skill) => (
                <div
                  key={skill.name}
                  className="bg-black/60 backdrop-blur-sm rounded-xl p-6
                            border border-[#45d6e9]/20"
                >
                  <h3 className="text-[#45d6e9] text-2xl font-medium mb-2">
                    {skill.name}
                  </h3>
                  <p className="text-gray-400 text-base">{skill.level}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Desktop Layout (lg and above) */}
      <section className="hidden lg:block py-16 relative z-[2]">
        <div className="container mx-auto px-6">
          <h1 className="text-4xl font-bold text-center text-[#45d6e9] mb-16 custom">
            About Me
          </h1>

          <div className="flex flex-col md:flex-row items-center gap-16 justify-center">
            {/* Profile Picture with Experience Badge */}
            <div className="w-[550px] h-[540px] relative">
              <div className="relative w-full h-full overflow-visible">
                <div className="relative w-full h-full overflow-hidden rounded-3xl bg-[#1a1a1a] shadow-2xl shadow-[#45d6e9]/20">
                  <Image
                    src="/asset/me1.png"
                    alt="Profile"
                    fill
                    sizes="(min-width: 1024px) 550px, 100vw"
                    className="object-cover object-top transition-transform duration-500 ease-in-out hover:scale-105"
                    priority
                  />
                </div>
                <div
                  className="absolute -bottom-8 -right-8 w-[160px] h-[90px] bg-black/80 backdrop-blur-sm
                    rounded-2xl border border-[#45d6e9]/20 flex flex-col items-center justify-center"
                >
                  <h3 className="text-[#45d6e9] text-3xl font-bold">1</h3>
                  <p className="text-gray-400 text-sm text-center leading-tight mt-1">
                    Years
                    <br />
                    Experience
                  </p>
                </div>
              </div>
            </div>

            {/* Bio and Skills */}
            <div className="w-full md:w-1/2 space-y-8">
              <div>
                <h2 className="text-4xl font-bold text-[#45d6e9] mb-6 raleway-font ">
                  Full Stack Web Developer
                </h2>
                <p className="text-gray-300 text-lg leading-relaxed raleway-font">
                  Hey, my name is{" "}
                  <span className="text-[#45d6e9] font-medium custom">
                    Keshav
                  </span>
                  , a dedicated MERN Developer with a passion for building
                  intuitive and responsive web applications. I enjoy crafting
                  clean, efficient code that transforms ideas into interactive
                  digital experiences.
                </p>
              </div>

              {/* Skills Grid */}
              <div className="grid grid-cols-2 gap-4">
                {[
                  { name: "MongoDB", level: "Intermediate Level" },
                  { name: "Express", level: "Intermediate Level" },
                  { name: "React", level: "Intermediate Level" },
                  { name: "Node.js", level: "Intermediate Level" },
                ].map((skill) => (
                  <div
                    key={skill.name}
                    className="bg-black/60 backdrop-blur-sm rounded-xl p-4
                    border border-[#45d6e9]/20 transform transition-all duration-300
                    hover:border-[#45d6e9]/40 hover:scale-105
                    pointer-events-auto custom" // Added pointer-events-auto
                  >
                    <h3 className="text-[#45d6e9] text-xl font-medium mb-1">
                      {skill.name}
                    </h3>
                    <p className="text-gray-400 text-sm raleway-font">
                      {skill.level}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Excellence Section */}
      <section className="py-16 relative z-[2]">
        <div className="container mx-auto px-6">
          <h2 className="text-4xl font-bold text-center text-[#45d6e9] mb-16 custom">
            Professional Excellence
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            <SpotlightCard>
              <div className="p-6 flex flex-col items-center text-center space-y-4">
                <span className="text-4xl">💡</span>
                <h3 className="text-xl font-semibold text-[#45d6e9] custom">
                  Innovation & Problem Solving
                </h3>
                <p className="text-gray-400 text-sm raleway-font">
                  Pioneering new solutions and tackling complex challenges with
                  creative approaches
                </p>
              </div>
            </SpotlightCard>

            <SpotlightCard>
              <div className="p-6 flex flex-col items-center text-center space-y-4">
                <span className="text-4xl">⚡</span>
                <h3 className="text-xl font-semibold text-[#45d6e9] custom ">
                  Clean Code & Performance
                </h3>
                <p className="text-gray-400 text-sm raleway-font">
                  Writing maintainable, scalable code optimized for maximum
                  efficiency
                </p>
              </div>
            </SpotlightCard>

            <SpotlightCard>
              <div className="p-6 flex flex-col items-center text-center space-y-4">
                <span className="text-4xl">👥</span>
                <h3 className="text-xl font-semibold text-[#45d6e9] custom">
                  User-Centric Development
                </h3>
                <p className="text-gray-400 text-sm raleway-font">
                  Building intuitive interfaces and experiences that prioritize
                  user satisfaction and engagement
                </p>
              </div>
            </SpotlightCard>

            <SpotlightCard>
              <div className="p-6 flex flex-col items-center text-center space-y-4">
                <span className="text-4xl">⚡</span>
                <h3 className="text-xl font-semibold text-[#45d6e9] custom">
                  On-Time Delivery
                </h3>
                <p className="text-gray-400 text-sm raleway-font">
                  Meeting project deadlines consistently while maintaining high
                  quality standards and attention to detail
                </p>
              </div>
            </SpotlightCard>

            <SpotlightCard>
              <div className="p-6 flex flex-col items-center text-center space-y-4">
                <span className="text-4xl">🤝</span>
                <h3 className="text-xl font-semibold text-[#45d6e9] custom">
                  Team Collaboration
                </h3>
                <p className="text-gray-400 text-sm raleway-font">
                  Working effectively in cross-functional teams to achieve
                  shared goals and project success
                </p>
              </div>
            </SpotlightCard>

            <SpotlightCard>
              <div className="p-6 flex flex-col items-center text-center space-y-4">
                <span className="text-4xl">📚</span>
                <h3 className="text-xl font-semibold text-[#45d6e9] custom">
                  Continuous Growth
                </h3>
                <p className="text-gray-400 text-sm raleway-font">
                  Staying updated with latest technologies and best practices in
                  web development
                </p>
              </div>
            </SpotlightCard>
          </div>
        </div>
      </section>

      {/* Dot pattern overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] z-[1]"></div>
    </div>
  );
};

export default SecondPage;
