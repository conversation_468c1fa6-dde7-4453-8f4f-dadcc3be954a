"use client";

import React, { useRef, useState } from "react";

interface Position {
  x: number;
  y: number;
}

interface SpotlightCardProps {
  children: React.ReactNode;
  className?: string;
  spotlightColor?: string;
}

const SpotlightCard: React.FC<SpotlightCardProps> = ({
  children,
  className = "",
  spotlightColor = "rgba(69, 214, 233, 0.1)",
}) => {
  const divRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState<number>(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>): void => {
    if (!divRef.current) return;
    const rect = divRef.current.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  return (
    <div
      ref={divRef}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setOpacity(1)}
      onMouseLeave={() => setOpacity(0)}
      className={`relative rounded-xl bg-[#1a1a1a] border border-[#45d6e9]/20 
                 overflow-hidden transition-colors duration-300 
                 hover:border-[#45d6e9]/40
                 hover:shadow-lg hover:shadow-[#45d6e9]/20
                 pointer-events-auto ${className}`}
    >
      {/* Spotlight effect */}
      <div
        className="pointer-events-none absolute inset-0 transition-opacity duration-300"
        style={{
          opacity,
          background: `radial-gradient(400px circle at ${position.x}px ${position.y}px, ${spotlightColor}, transparent 40%)`,
        }}
      />

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
  );
};

export default SpotlightCard;

{
  /* Professional Excellence Section */
}
<section className="py-16 relative z-[2]">
  <div className="container mx-auto px-6">
    <h2 className="text-4xl font-bold text-center text-[#45d6e9] mb-16">
      Professional Excellence
    </h2>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
      <SpotlightCard>
        <div className="p-6 flex flex-col items-center text-center space-y-4">
          <span className="text-4xl">💡</span>
          <h3 className="text-xl font-semibold text-[#45d6e9]">
            Innovation & Problem Solving
          </h3>
          <p className="text-gray-400 text-sm">
            Pioneering new solutions and tackling complex challenges with
            creative approaches
          </p>
        </div>
      </SpotlightCard>

      <SpotlightCard>
        <div className="p-6 flex flex-col items-center text-center space-y-4">
          <span className="text-4xl">⚡</span>
          <h3 className="text-xl font-semibold text-[#45d6e9]">
            Clean Code & Performance
          </h3>
          <p className="text-gray-400 text-sm">
            Writing maintainable, scalable code optimized for maximum efficiency
          </p>
        </div>
      </SpotlightCard>
    </div>
  </div>
</section>;
