import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: false, // Set this to false to fix the findDOMNode error
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  webpack: (config:any) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };
    return config;
  },
};

export default nextConfig;
  
