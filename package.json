{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "appwrite": "^17.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.0.6", "gsap": "^3.13.0", "lucide-react": "^0.503.0", "motion": "^12.9.2", "next": "15.3.1", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.3.1", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "tech-stack-icons": "^2.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}