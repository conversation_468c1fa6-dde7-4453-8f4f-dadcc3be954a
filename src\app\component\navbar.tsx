"use client";
import React, { useState, useEffect } from "react";
import GradientText from "../ui/GradientText";

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [visible, setVisible] = useState(true);
  const [prevScrollPos, setPrevScrollPos] = useState(0);

  // Shared navigation function
  const scrollToSection = (sectionId: string) => {
    // First close mobile menu
    setIsMobileMenuOpen(false);

    // Wait for menu animation to complete
    setTimeout(() => {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }, 300);
  };

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollPos = window.scrollY;

      setVisible(prevScrollPos > currentScrollPos || currentScrollPos < 10);

      setPrevScrollPos(currentScrollPos);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [prevScrollPos]);

  return (
    <>
      {/* Mobile & Tablet Navbar (sm and md screens) */}
      <div
        className={`block lg:hidden fixed top-0 left-0 w-full z-50 transition-transform duration-300 ${
          visible ? "translate-y-0" : "-translate-y-full"
        }`}
      >
        <div className="w-full bg-black/30 backdrop-blur-sm px-6 py-4 flex justify-between items-center">
          <div className="text-xl md:text-2xl font-bold">
            <span className="text-[#45d6e9] custom">Keshav</span>
            <span className="text-[#0f92a9] custom">.dev</span>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="text-[#45d6e9] p-2"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="3" y1="12" x2="21" y2="12" />
              <line x1="3" y1="6" x2="21" y2="6" />
              <line x1="3" y1="18" x2="21" y2="18" />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile & Tablet Menu Overlay */}
      <div
        className={`fixed inset-0 bg-black/95 z-50 lg:hidden transition-all duration-500 ease-in-out ${
          isMobileMenuOpen
            ? "opacity-100 pointer-events-auto"
            : "opacity-0 pointer-events-none"
        }`}
      >
        <div
          className={`flex flex-col h-full transform transition-all duration-500 ease-out ${
            isMobileMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
        >
          {/* Close Button - Fade and Rotate Animation */}
          <div
            className={`flex justify-end p-6 transform transition-all duration-500 delay-200 ${
              isMobileMenuOpen
                ? "opacity-100 translate-y-0"
                : "opacity-0 -translate-y-4"
            }`}
          >
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="text-[#45d6e9] p-2 hover:rotate-180 transition-all duration-300"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>
          </div>

          {/* Menu Items - Staggered Animation */}
          <div className="flex flex-col items-center justify-center text-white flex-1 space-y-8">
            {[
              {
                component: (
                  <MobileNavItem
                    text="About"
                    onClick={() => scrollToSection("about")}
                  />
                ),
                delay: "delay-[200ms]",
              },
              {
                component: (
                  <MobileNavItem
                    text="Projects"
                    onClick={() => scrollToSection("projects")}
                  />
                ),
                delay: "delay-[300ms]",
              },
              {
                component: (
                  <MobileNavItem
                    text="Skills"
                    onClick={() => scrollToSection("skills")}
                  />
                ),
                delay: "delay-[400ms]",
              },
              {
                component: (
                  <MobileMysteryBoard
                    onClick={() => scrollToSection("mysteryboard")}
                  />
                ),
                delay: "delay-[500ms]",
              },
              {
                component: (
                  <MobileNavItem
                    text="Contact"
                    onClick={() => scrollToSection("contact")}
                  />
                ),
                delay: "delay-[600ms]",
              },
            ].map((item, index) => (
              <div
                key={index}
                className={`transform transition-all duration-500 ${
                  item.delay
                } ${
                  isMobileMenuOpen
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-8"
                }`}
              >
                {item.component}
              </div>
            ))}

            {/* Hire Me Button - Special Animation */}
            <div
              onClick={() => scrollToSection("contact")}
              className={`transform transition-all duration-500 delay-[700ms] ${
                isMobileMenuOpen
                  ? "opacity-100 translate-y-0 scale-100"
                  : "opacity-0 translate-y-8 scale-95"
              }`}
            >
              <GradientText
                colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
                animationSpeed={3}
                showBorder={true}
                className="custom cursor-pointer mt-8"
              >
                Hire me!
              </GradientText>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Navbar (lg screens and above) */}
      <div
        className={`hidden lg:flex w-full fixed top-0 bg-black/30 text-white py-6 px-8 justify-between items-center z-50 transition-transform duration-300 ${
          visible ? "translate-y-0" : "-translate-y-full"
        }`}
      >
        <div className="text-2xl font-bold">
          <span className="text-[#45d6e9] custom">Keshav</span>
          <span className="text-[#0f92a9] custom">.dev</span>
        </div>

        <div className="flex-1 flex justify-center">
          <NavItem text="About" onClick={() => scrollToSection("about")} />
          <div className="flex items-center space-x-8 raleway-font">
            <NavItem
              text="Projects"
              onClick={() => scrollToSection("projects")}
            />
            <NavItem text="Skills" onClick={() => scrollToSection("skills")} />
            <div
              className={`relative px-4 py-2 rounded-md ${
                isHovered ? "bg-[#45d6e9]/20" : "bg-transparent"
              } transition-all duration-300 ease-in-out cursor-pointer flex items-center`}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={() => scrollToSection("mysteryboard")}
            >
              <span className="relative z-10 font-medium raleway-font">
                Mystery Board
              </span>
              <span className="ml-1">✨</span>
            </div>
            <NavItem
              text="Contact"
              onClick={() => scrollToSection("contact")}
            />
          </div>
        </div>
        <div onClick={() => scrollToSection("contact")}>
          <GradientText
            colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
            animationSpeed={3}
            showBorder={true}
            className="custom cursor-pointer"
          >
            Hire me!
          </GradientText>
        </div>
      </div>
    </>
  );
};

const MobileNavItem = ({
  text,
  onClick,
}: {
  text: string;
  onClick: () => void;
}) => {
  const [isPressed, setIsPressed] = useState(false);

  return (
    <div
      className="relative px-4 py-2 rounded-md group cursor-pointer"
      onTouchStart={() => setIsPressed(true)}
      onTouchEnd={() => setIsPressed(false)}
      onClick={onClick}
    >
      <div
        className={`absolute inset-0 rounded-md transition-all duration-300 ease-in-out ${
          isPressed ? "opacity-100" : "opacity-0"
        }`}
      >
        <div
          className="absolute inset-0 opacity-50 blur-xl"
          style={{
            background: "linear-gradient(90deg, #45d6e9, #0f92a9, #45d6e9)",
          }}
        />

        <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:4px_4px] opacity-[0.1]" />
      </div>

      <span className="relative z-10 text-2xl font-medium transition-colors duration-300 ease-in-out group-active:text-[#45d6e9]">
        {text}
      </span>

      <div
        className={`absolute bottom-0 left-0 h-[2px] bg-[#45d6e9] transition-all duration-300 ease-in-out ${
          isPressed ? "w-full opacity-100" : "w-0 opacity-0"
        }`}
        style={{
          background: "linear-gradient(90deg, #45d6e9, #0f92a9, #45d6e9)",
        }}
      />
    </div>
  );
};

const NavItem = ({ text, onClick }: { text: string; onClick: () => void }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="relative px-4 py-2 rounded-md group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      <div
        className={`absolute inset-0 rounded-md transition-all duration-300 ease-in-out ${
          isHovered ? "opacity-100" : "opacity-0"
        }`}
      >
        <div
          className="absolute inset-0 opacity-50 blur-xl"
          style={{
            background: "linear-gradient(90deg, #45d6e9, #0f92a9, #45d6e9)",
          }}
        />

        <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:4px_4px] opacity-[0.1]" />
      </div>

      <span className="relative z-10 font-medium transition-colors duration-300 ease-in-out group-hover:text-[#45d6e9]">
        {text}
      </span>

      <div
        className={`absolute bottom-0 left-0 h-[2px] bg-[#45d6e9] transition-all duration-300 ease-in-out ${
          isHovered ? "w-full opacity-100" : "w-0 opacity-0"
        }`}
        style={{
          background: "linear-gradient(90deg, #45d6e9, #0f92a9, #45d6e9)",
        }}
      />
    </div>
  );
};

const MobileMysteryBoard = ({ onClick }: { onClick: () => void }) => {
  const [isPressed, setIsPressed] = useState(false);

  return (
    <div
      className={`relative px-4 py-2 rounded-md ${
        isPressed ? "bg-[#45d6e9]/20" : "bg-transparent"
      } transition-all duration-300 ease-in-out cursor-pointer flex items-center`}
      onTouchStart={() => setIsPressed(true)}
      onTouchEnd={() => setIsPressed(false)}
      onClick={onClick}
    >
      <span className="relative z-10 text-2xl font-medium raleway-font">
        Mystery Board
      </span>
      <span className="ml-1">✨</span>
    </div>
  );
};

export default Navbar;
