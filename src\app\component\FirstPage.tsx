"use client";
import React, { useEffect, useRef } from "react";
import Image from "next/image";
import { InstagramIcon } from "@/app/ui/instagram";
import Button from "../ui/AnimatedButton";
import { LinkedinIcon } from "../ui/linkedin";
import { GithubIcon } from "../ui/github";
import SplashCursor from "../ui/SplashCursor";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);
const handleDownloadCV = () => {
  const link = document.createElement("a");
  link.href = "/asset/keshav_new.pdf";
  link.download = "keshavResume.pdf";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
const FirstPage = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const mobileRef = useRef<HTMLDivElement>(null);
  const tabletRef = useRef<HTMLDivElement>(null);
  const desktopRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Pin first page with shorter scroll distance
      if (containerRef.current) {
        ScrollTrigger.create({
          trigger: containerRef.current,
          start: "top top",
          end: "+=50vh", // Reduced scroll distance
          pin: true,
          pinSpacing: false,
        });
      }

      // Mobile animations
      if (mobileRef.current) {
        const mobileElements =
          mobileRef.current.querySelectorAll(".animate-element");
        gsap.set(mobileElements, {
          opacity: 0,
          y: 50,
          scale: 0.9,
        });

        gsap.to(mobileElements, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          stagger: 0.15,
          ease: "power3.out",
          scrollTrigger: {
            trigger: mobileRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        });
      }

      // Tablet animations
      if (tabletRef.current) {
        const tabletElements =
          tabletRef.current.querySelectorAll(".animate-element");
        gsap.set(tabletElements, {
          opacity: 0,
          y: 60,
          scale: 0.95,
        });

        gsap.to(tabletElements, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.4,
          stagger: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: tabletRef.current,
            start: "top 75%",
            end: "bottom 25%",
            toggleActions: "play none none reverse",
          },
        });
      }

      // Desktop animations
      if (desktopRef.current) {
        const desktopElements =
          desktopRef.current.querySelectorAll(".animate-element");
        gsap.set(desktopElements, {
          opacity: 0,
          y: 80,
          scale: 0.9,
        });

        gsap.to(desktopElements, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.6,
          stagger: 0.25,
          ease: "power3.out",
          scrollTrigger: {
            trigger: desktopRef.current,
            start: "top 70%",
            end: "bottom 30%",
            toggleActions: "play none none reverse",
          },
        });

        // Special floating animation for profile image
        const profileImage = desktopRef.current.querySelector(".profile-image");
        if (profileImage) {
          gsap.set(profileImage, {
            opacity: 0,
            x: 100,
            rotation: 5,
          });

          gsap.to(profileImage, {
            opacity: 1,
            x: 0,
            rotation: 0,
            duration: 1.8,
            ease: "power3.out",
            scrollTrigger: {
              trigger: desktopRef.current,
              start: "top 60%",
              toggleActions: "play none none reverse",
            },
          });
        }
      }

      // Smooth parallax effect for background elements
      gsap.utils.toArray(".parallax-bg").forEach((element) => {
        gsap.to(element as Element, {
          yPercent: -50,
          ease: "none",
          scrollTrigger: {
            trigger: element as Element,
            start: "top bottom",
            end: "bottom top",
            scrub: true,
          },
        });
      });
    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div
      ref={containerRef}
      className="min-h-screen w-full max-w-full overflow-x-hidden relative z-10 will-change-transform"
      style={{ transformOrigin: "center center" }}
    >
      <>
        <SplashCursor />
        {/* Mobile View */}
        <div
          ref={mobileRef}
          className="md:hidden min-h-screen w-full max-w-full relative overflow-x-hidden overflow-y-hidden animated-gradient-bg pt-20"
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50"></div>

          <div className="px-4 pb-8">
            {/* Profile Image Section */}
            <div className="animate-element w-[250px] h-[250px] mx-auto rounded-3xl overflow-hidden mb-8 relative">
              <Image
                src="/asset/Me.jpg"
                alt="Profile"
                fill
                sizes="(max-width: 640px) 250px, 100vw"
                className="object-cover object-top"
                priority
              />
              <div
                className="absolute top-4 right-4 bg-[#0a2a1c]/60 backdrop-blur-sm text-[#45d6e9]
                              px-4 py-2 rounded-full text-sm border border-[#45d6e9]/30 raleway-font"
              >
                Available for hire
              </div>
            </div>

            {/* Content Section */}
            <div className="space-y-4">
              <p className="animate-element text-[#45d6e9] text-sm tracking-wider uppercase raleway-font">
                WELCOME TO MY PORTFOLIO
              </p>

              <h1 className="animate-element text-4xl font-bold text-white raleway-font">
                Hey, I&apos;m Keshav
              </h1>

              <div className="space-y-2">
                <h2 className="animate-element text-[#45d6e9] text-5xl font-bold custom">
                  Transforming
                  <br />
                  Ideas into
                  <br />
                  code
                </h2>

                <p className="animate-element text-[#45d6e9] text-xl raleway-font">
                  Full Stack Web Developer
                </p>

                <p className="animate-element text-gray-400 text-lg leading-relaxed raleway-font">
                  Transforming ideas into elegant digital solutions. I
                  specialize in creating seamless, user-centric experiences that
                  blend innovation with functionality.
                </p>
              </div>

              {/* CTA Button */}
              <div className="animate-element pt-6">
                <Button
                  onClick={handleDownloadCV}
                  colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
                  className="w-full bg-[#0a2a1c] border border-[#45d6e9]/30 raleway-font"
                >
                  DOWNLOAD CV
                </Button>
              </div>

              {/* Social Icons - Mobile Version */}
              <div className="animate-element flex justify-center gap-8 pt-6">
                {[
                  {
                    Icon: LinkedinIcon,
                    link: "https://linkedin.com/in/its-keshavraj",
                    isCustom: true,
                  },
                  {
                    Icon: GithubIcon,
                    link: "https://github.com/itz-rajkeshav",
                    isCustom: true,
                  },
                  {
                    Icon: InstagramIcon,
                    link: "https://www.instagram.com/user.bot__/",
                    isCustom: true,
                  },
                ].map((item, index) => (
                  <a
                    key={index}
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:scale-110 transition-transform duration-300"
                  >
                    <item.Icon className="w-10 h-10 text-[#45d6e9] brightness-150" />
                  </a>
                ))}
              </div>
            </div>
          </div>

          <div className="parallax-bg absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] pointer-events-none"></div>
        </div>

        {/* Tablet View */}
        <div
          ref={tabletRef}
          className="hidden md:block lg:hidden min-h-screen w-full max-w-full relative overflow-x-hidden overflow-y-hidden animated-gradient-bg"
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50"></div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 pt-28 pb-16 flex flex-col-reverse items-center relative z-10 gap-8">
            <div className="flex-1 space-y-6 text-center">
              <p className="animate-element text-[#45d6e9] text-sm sm:text-md raleway-font tracking-wider">
                WELCOME TO MY PORTFOLIO
              </p>

              <h1 className="animate-element text-4xl sm:text-5xl font-bold text-white raleway-font">
                Hey, I&apos;m Keshav :)
              </h1>

              <div className="space-y-4">
                <h3 className="animate-element text-[#45d6e9] text-4xl sm:text-5xl font-bold custom">
                  Transforming Ideas
                  <br />
                  into Code
                </h3>

                <p className="animate-element text-[#45d6e9] text-xl raleway-font">
                  Full Stack Web Developer
                </p>

                <p className="animate-element text-gray-300 max-w-lg mx-auto raleway-font leading-relaxed">
                  I transform ideas into refined digital products, blending
                  innovative thinking with user-centric design to deliver
                  seamless and functional experiences.
                </p>

                <div className="animate-element flex flex-col items-center gap-6">
                  <Button
                    colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
                    onClick={handleDownloadCV}
                  >
                    Download CV
                  </Button>

                  <div className="flex items-center gap-6">
                    {[
                      {
                        Icon: LinkedinIcon,
                        link: "https://linkedin.com/in/its-keshavraj",
                        isCustom: true,
                      },
                      {
                        Icon: GithubIcon,
                        link: "https://github.com/itz-rajkeshav",
                        isCustom: true,
                      },
                      {
                        Icon: InstagramIcon,
                        link: "https://www.instagram.com/user.bot__/",
                        isCustom: true,
                      },
                    ].map((item, index) => (
                      <a
                        key={index}
                        href={item.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`
                          hover:scale-110 transition-transform duration-300
                          ${!item.isCustom ? "hover:text-[#45d6e9]" : ""}
                        `}
                      >
                        <item.Icon className="text-[#45d6e9]" />
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Profile Image */}
            <div className="animate-element w-[400px] h-[420px] mb-8">
              <div
                className="relative w-full h-full overflow-hidden rounded-3xl shadow-2xl shadow-[#45d6e9]/20
                           transform transition-all duration-500 ease-in-out
                           hover:shadow-[#45d6e9]/40 hover:shadow-3xl"
              >
                <Image
                  src="/asset/Me.jpg"
                  alt="Profile"
                  fill
                  sizes="(min-width: 640px) and (max-width: 1024px) 400px, 100vw"
                  className="object-cover object-top transition-transform duration-500 ease-in-out hover:scale-110"
                  priority
                />
                <div
                  className="absolute top-4 right-4 bg-[#45d6e9]/20 backdrop-blur-sm text-white
                                px-4 py-2 rounded-3xl raleway-font text-sm border border-[#45d6e9]/30
                                transition-all duration-500 ease-in-out
                                hover:bg-[#45d6e9]/30"
                >
                  Available for hire
                </div>
              </div>
            </div>
          </div>
          <div className="parallax-bg absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1]"></div>
        </div>

        {/* Desktop View */}
        <div
          ref={desktopRef}
          className="hidden lg:block min-h-screen w-full max-w-full relative overflow-x-hidden overflow-y-hidden animated-gradient-bg"
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50"></div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 sm:pt-32 pb-16 flex flex-col lg:flex-row justify-between items-center relative z-10 gap-12">
            {/* Left Content */}
            <div className="flex-1 space-y-6 text-center lg:text-left">
              <p className="animate-element text-[#45d6e9] text-sm sm:text-md raleway-font tracking-wider">
                WELCOME TO MY PORTFOLIO
              </p>

              <h1 className="animate-element text-4xl sm:text-5xl lg:text-6xl font-bold text-white raleway-font">
                Hey, I&apos;m Keshav :)
              </h1>

              <div className="space-y-4 sm:space-y-6">
                <h3 className="animate-element text-[#45d6e9] text-4xl sm:text-5xl lg:text-6xl font-bold custom">
                  Transforming Ideas
                  <br />
                  into Code
                </h3>

                <p className="animate-element text-[#45d6e9] text-xl raleway-font">
                  Full Stack Web Developer
                </p>

                <p className="animate-element text-gray-300 max-w-lg raleway-font leading-relaxed">
                  I transform ideas into refined digital products, blending
                  innovative thinking with user-centric design to deliver
                  seamless and functional experiences.
                </p>

                <div className="animate-element flex items-center space-x-14">
                  <Button
                    onClick={handleDownloadCV}
                    colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
                  >
                    Download CV
                  </Button>

                  <div className="flex items-center gap-6">
                    {[
                      {
                        Icon: LinkedinIcon,
                        link: "https://linkedin.com/in/its-keshavraj",
                        isCustom: true,
                      },
                      {
                        Icon: GithubIcon,
                        link: "https://github.com/itz-rajkeshav",
                        isCustom: true,
                      },
                      {
                        Icon: InstagramIcon,
                        link: "https://www.instagram.com/user.bot__/",
                        isCustom: true,
                      },
                    ].map((item, index) => (
                      <a
                        key={index}
                        href={item.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`
                          hover:scale-110 transition-transform duration-300
                          ${!item.isCustom ? "hover:text-[#45d6e9]" : ""}
                        `}
                      >
                        <item.Icon className="text-[#45d6e9]" />
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content - Profile Image */}
            <div className="profile-image w-[450px] h-[470px]">
              <div
                className="relative w-full h-full overflow-hidden rounded-3xl shadow-2xl shadow-[#45d6e9]/20
                           transform transition-all duration-500 ease-in-out
                           hover:shadow-[#45d6e9]/40 hover:shadow-3xl"
              >
                <Image
                  src="/asset/Me.jpg"
                  alt="Profile"
                  fill
                  sizes="(min-width: 1024px) 450px, 100vw"
                  className="object-cover object-top transition-transform duration-500 ease-in-out hover:scale-110"
                  priority
                />
                <div
                  className="absolute top-4 right-4 bg-[#45d6e9]/20 backdrop-blur-sm text-white
                            px-4 py-2 rounded-3xl raleway-font text-sm border border-[#45d6e9]/30
                            transition-all duration-500 ease-in-out
                            hover:bg-[#45d6e9]/30"
                >
                  Available for hire
                </div>
              </div>
            </div>
          </div>
          <div className="parallax-bg absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1]"></div>
        </div>
      </>
    </div>
  );
};

export default FirstPage;
