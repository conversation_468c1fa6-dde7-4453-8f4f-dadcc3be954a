"use client";
import React, { useState, useEffect, useRef } from "react";
import SpotlightCard from "../ui/SpotlightCard";
import StackIcon from "tech-stack-icons";
import { motion } from "framer-motion";

const skills = {
  all: [
    { name: "JavaScript", icon: "js", type: "frontend" },
    { name: "MongoDB", icon: "mongodb", type: "backend" },
    { name: "Express.js", icon: "express", type: "backend" },
    { name: "React", icon: "reactjs", type: "frontend" },
    { name: "Next.js", icon: "nextjs2", type: "frontend" },
    { name: "Node.js", icon: "nodejs", type: "backend" },
    { name: "Tailwind CSS", icon: "tailwindcss", type: "frontend" },
    { name: "GitHub", icon: "github", type: "tools" },
    { name: "Git", icon: "git", type: "tools" },
    { name: "PostgreSQL", icon: "postgresql", type: "backend" },
    { name: "Redux", icon: "redux", type: "frontend" },
    { name: "TypeScript", icon: "typescript", type: "frontend" },
    { name: "Vite", icon: "vitejs", type: "tools" },
    { name: "Docker", icon: "docker", type: "tools" },
    { name: "Supabase", icon: "supabase", type: "backend" },
    { name: "Linux", icon: "linux", type: "tools" },
    { name: "Firebase", icon: "firebase", type: "backend" },
    { name: "C++", icon: "c++", type: "tools" },
  ],
};

const SkillPage = () => {
  const [activeFilter, setActiveFilter] = useState("all");
  const [isInView, setIsInView] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const filteredSkills = skills.all.filter(
    (skill) => activeFilter === "all" || skill.type === activeFilter
  );

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isInView) {
          setIsInView(true);
        }
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [isInView]);

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter);
  };

  const cardVariants = {
    animate: (index: number) => ({
      rotateY: [0, 360], // Full rotation around Y axis
      transition: {
        duration: 0.8,
        ease: "easeOut",
        delay: index * 0.1,
      },
    }),
  };

  return (
    <div
      id="skills"
      className="min-h-screen relative overflow-hidden animated-gradient-bg"
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50 z-[1]"></div>

      {/* Background patterns */}
      <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] z-[1]"></div>
      <div className="absolute inset-0 bg-[conic-gradient(at_top_left,#45d6e9_10%,transparent_30%,transparent_70%,#45d6e9_90%)] opacity-[0.15] [background-size:30px_30px] scale-[1.5] mix-blend-overlay z-[1]"></div>

      <div className="max-w-7xl mx-auto px-4 py-20 relative z-[2]">
        {/* Skills Section */}
        <section className="mb-32">
          <h1 className="text-5xl font-bold text-center text-[#45d6e9] mb-4 custom">
            My Skills
          </h1>
          <p className="text-center text-gray-400 mb-12 max-w-2xl mx-auto font-se raleway-font">
            I put your ideas and thus your wishes in the form of a unique web
            project that inspires you and your customers.
          </p>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-12 px-4">
            <div className="flex flex-wrap justify-center gap-4">
              {["All", "Frontend", "Backend", "Tools"].map((filter) => (
                <button
                  key={filter}
                  onClick={() => handleFilterChange(filter.toLowerCase())}
                  className={`px-4 sm:px-6 py-2 rounded-full transition-all duration-300 text-sm sm:text-base raleway-font ${
                    activeFilter === filter.toLowerCase()
                      ? "bg-[#45d6e9] text-black"
                      : "bg-[#1a1a1a] text-white hover:bg-[#45d6e9]/20"
                  }`}
                >
                  {filter}
                </button>
              ))}
            </div>
          </div>

          {/* Skills Grid */}
          <div ref={containerRef} className="max-w-6xl mx-auto">
            <div
              className={`grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-8 ${
                activeFilter !== "all" ? "justify-center" : ""
              }`}
            >
              {filteredSkills.map((skill, index) => (
                <motion.div
                  key={`${skill.name}-${activeFilter}`}
                  custom={index}
                  animate={isInView ? "animate" : { rotateY: 0 }}
                  variants={cardVariants}
                  style={{ perspective: "1000px" }} // Adds 3D effect
                  className="flex flex-col items-center gap-3"
                >
                  <SpotlightCard className="w-full aspect-square flex items-center justify-center p-4 group transform transition-all duration-300 hover:scale-105">
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="filter grayscale group-hover:grayscale-0 transition-all duration-300 transform group-hover:scale-110 group-hover:rotate-3">
                        <SkillIcon icon={skill.icon} />
                      </div>
                    </div>
                  </SpotlightCard>
                  <span className="text-[#45d6e9] text-sm font-medium raleway-font">
                    {skill.name}
                  </span>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Educational Journey Section */}
        <section>
          <h1 className="text-5xl font-bold text-center text-[#45d6e9] mb-4 custom">
            Educational Journey
          </h1>
          <p className="text-center font-semibold text-gray-400 mb-12 max-w-2xl mx-auto raleway-font">
            Laying the groundwork for future advancements in technology and
            innovation
          </p>

          {/* Mobile Layout */}
          <div className="lg:hidden relative max-w-md mx-auto px-4">
            {/* Timeline line */}
            <div className="absolute left-4 top-0 bottom-0 w-1">
              <div
                className="w-full h-full opacity-20"
                style={{
                  background:
                    "linear-gradient(180deg, #45d6e9 0%, #45d6e9 85%, transparent 100%)",
                }}
              ></div>
            </div>

            <div className="space-y-8">
              {/* First Education Card */}
              <div className="relative pl-12">
                {/* Timeline dot */}
                <div className="absolute left-3 top-4 transform -translate-x-1/2">
                  <div className="w-3 h-3 rounded-full bg-[#45d6e9] shadow-lg shadow-[#45d6e9]/50 relative">
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-ping opacity-75"></div>
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-pulse"></div>
                  </div>
                </div>

                <div className="bg-[#0A0A0A]/80 rounded-lg backdrop-blur-sm border border-[#45d6e9]/10 p-6">
                  <h3 className="text-2xl font-semibold">
                    <span className="text-white">Class</span>{" "}
                    <span className="text-gray-400">XII</span>
                  </h3>
                  <h4 className="text-lg text-[#45d6e9] mt-2">
                    Adarsh Vikash Vidhyalaya
                  </h4>
                  <div className="flex flex-col gap-2 mt-2">
                    <span className="text-[#45d6e9]">
                      March 2022 - March 2023
                    </span>
                    <span className="text-[#45d6e9]">Percent: 75%</span>
                  </div>
                  <p className="text-gray-400 mt-4">
                    Completed Class XII with a focus on Physics, Chemistry, and
                    Mathematics (PCM).
                  </p>
                  <div className="flex flex-col gap-2 mt-4">
                    <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm">
                      Took part in academic presentations and collaborative
                      projects.
                    </span>
                    <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm">
                      Built strong foundations in communication, and leadership
                      skills.
                    </span>
                  </div>
                </div>
              </div>

              {/* Second Education Card */}
              <div className="relative pl-12">
                {/* Timeline dot */}
                <div className="absolute left-3 top-4 transform -translate-x-1/2">
                  <div className="w-3 h-3 rounded-full bg-[#45d6e9] shadow-lg shadow-[#45d6e9]/50 relative">
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-ping opacity-75"></div>
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-pulse"></div>
                  </div>
                </div>

                <div className="bg-[#0A0A0A]/80 rounded-lg backdrop-blur-sm border border-[#45d6e9]/10 p-6">
                  <h3 className="text-2xl font-semibold">
                    <span className="text-white">
                      Bachelor of Technology in
                    </span>{" "}
                    <span className="text-gray-400">
                      Computer Science and Engineering
                    </span>
                  </h3>
                  <h4 className="text-lg text-[#45d6e9] mt-2">
                    Government Engineering College, Buxar
                  </h4>
                  <div className="flex items-center gap-4 mt-2">
                    <span className="text-[#45d6e9]">
                      September 2024 - July 2027
                    </span>
                  </div>
                  <p className="text-gray-400 mt-4">
                    Pursuing B.Tech with focus on full-stack development,
                    software engineering, and system design.
                  </p>
                  <div className="flex flex-col gap-2 mt-4">
                    <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm inline-block">
                      Enrolled for Sep 2024
                    </span>
                    <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm inline-block">
                      Will explore more in software development, system design,
                      and real-world projects
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:block relative max-w-6xl mx-auto">
            {/* Timeline line with gradient fade */}
            <div className="absolute left-1/2 transform -translate-x-[2px] h-full w-1">
              <div
                className="w-full h-full bg-gradient-to-b from-[#45d6e9] via-[#45d6e9] to-transparent opacity-20"
                style={{
                  background:
                    "linear-gradient(180deg, #45d6e9 0%, #45d6e9 85%, transparent 100%)",
                }}
              ></div>
            </div>

            <div className="space-y-32">
              {/* First Education Card - Left Side */}
              <div className="relative flex justify-start w-full">
                <div className="w-[calc(50%-20px)] bg-[#0A0A0A]/80 rounded-lg backdrop-blur-sm border border-[#45d6e9]/10">
                  <div className="p-8">
                    <h3 className="text-2xl font-semibold">
                      <span className="text-white">Class</span>{" "}
                      <span className="text-gray-400">XII</span>
                    </h3>
                    <h4 className="text-lg text-[#45d6e9] mt-2">
                      Adarsh Vikash Vidhyalaya
                    </h4>
                    <div className="flex items-center gap-4 mt-2">
                      <span className="text-[#45d6e9]">
                        March 2022 - March 2023
                      </span>
                      <span className="text-[#45d6e9]">Percent: 75%</span>
                    </div>
                    <p className="text-gray-400 mt-4">
                      Completed Class XII with a focus on Physics, Chemistry,
                      and Mathematics (PCM).
                    </p>
                    <div className="flex flex-col gap-2 mt-4">
                      <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm inline-block">
                        Took part in academic presentations and collaborative
                        projects.{" "}
                      </span>
                      <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm inline-block">
                        Built strong foundations in communication, organization,
                        and leadership skills.
                      </span>
                    </div>
                  </div>
                </div>
                {/* Timeline dot */}
                <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="w-3 h-3 rounded-full bg-[#45d6e9] shadow-lg shadow-[#45d6e9]/50 relative">
                    {/* Pulsing animation rings */}
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-ping opacity-75"></div>
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-pulse"></div>
                  </div>
                </div>
              </div>

              {/* Second Education Card - Right Side */}
              <div className="relative flex justify-end w-full">
                <div className="w-[calc(50%-20px)] bg-[#0A0A0A]/80 rounded-lg backdrop-blur-sm border border-[#45d6e9]/10">
                  <div className="p-8">
                    <h3 className="text-2xl font-semibold">
                      <span className="text-white">
                        Bachelor of Technology in
                      </span>{" "}
                      <span className="text-gray-400">
                        Computer Science and Engineering
                      </span>
                    </h3>
                    <h4 className="text-lg text-[#45d6e9] mt-2">
                      Government Engineering College, Buxar
                    </h4>
                    <div className="flex items-center gap-4 mt-2">
                      <span className="text-[#45d6e9]">
                        September 2024 - July 2027
                      </span>
                    </div>
                    <p className="text-gray-400 mt-4">
                      Pursuing B.Tech with focus on full-stack development,
                      software engineering, and system design.
                    </p>
                    <div className="flex flex-col gap-2 mt-4">
                      <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm inline-block">
                        Enrolled for Sep 2024
                      </span>
                      <span className="bg-[#45d6e9]/10 text-[#45d6e9] px-4 py-2 rounded-full text-sm inline-block">
                        Will explore more in software development, system
                        design, and real-world projects
                      </span>
                    </div>
                  </div>
                </div>
                {/* Timeline dot */}
                <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="w-3 h-3 rounded-full bg-[#45d6e9] shadow-lg shadow-[#45d6e9]/50 relative">
                    {/* Pulsing animation rings */}
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-ping opacity-75"></div>
                    <div className="absolute inset-0 rounded-full bg-[#45d6e9] animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

const SkillIcon = ({ icon }: { icon: string }) => {
  if (icon === "express") {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 128 128"
        className="w-16 h-16"
        style={{ filter: "brightness(6.2)", fill: "currentColor" }}
      >
        <path d="M40.53 77.82V50.74H42V55a5.57 5.57 0 00.48-.6 7.28 7.28 0 016.64-4.12c3.35-.1 6.07 1.14 7.67 4.12a13.24 13.24 0 01.32 12.14c-1.49 3.34-5.17 5-9.11 4.39a7.37 7.37 0 01-5.88-3.88v10.77zM42 60.32c.13 1.32.18 2.26.33 3.18.58 3.62 2.72 5.77 6.08 6.16A6.91 6.91 0 0056 65.27a11.77 11.77 0 00-.26-9.68 6.77 6.77 0 00-7.13-3.94 6.59 6.59 0 00-5.89 4.87 33.4 33.4 0 00-.72 3.8zM88.41 64a7.92 7.92 0 01-7.74 7c-6.16.31-9.05-3.78-9.51-8.5a13.62 13.62 0 011.2-7.5 8.37 8.37 0 018.71-4.67 8 8 0 017.1 6.09 41.09 41.09 0 01.69 4.5H72.67c-.3 4.28 2 7.72 5.26 8.55 4.06 1 7.53-.76 8.79-4.62.28-.99.79-1.13 1.69-.85zm-15.74-4.45h14.47c-.09-4.56-2.93-7.86-6.78-7.91-4.36-.07-7.5 3.11-7.69 7.91zM91.39 64.1h1.42a5.69 5.69 0 003.34 4.9 8.73 8.73 0 007.58-.2 3.41 3.41 0 002-3.35 3.09 3.09 0 00-2.08-3.09c-1.56-.58-3.22-.9-4.81-1.41A35.25 35.25 0 0194 59.18c-2.56-1.25-2.72-6.12.18-7.66a10.21 10.21 0 019.76-.15 5.14 5.14 0 012.6 5.52h-1.22c0-.4-.09-.76-.15-1.12-.52-3.35-3.95-4.59-7.76-3.81-2.13.45-3.57 1.47-3.58 3.37a3.14 3.14 0 002 3.18c1.54.56 3.15.92 4.73 1.36 1.27.35 2.59.58 3.82 1a4.62 4.62 0 013.32 4.18 4.52 4.52 0 01-2.58 4.74 11.48 11.48 0 01-10.4.241c-2.58-1.06-3.61-3.09-3.33-5.84zM125.21 56.61h-1.33c0-.43-.07-.82-.14-1.21-.56-3.37-4.05-4.61-7.82-3.82-2.23.47-3.6 1.55-3.6 3.35a3.14 3.14 0 002 3.18c1.54.56 3.16.92 4.74 1.36 1.27.35 2.59.58 3.82 1a4.62 4.62 0 013.32 4.18 4.52 4.52 0 01-2.58 4.74 11.48 11.48 0 01-10.4.24c-2.58-1.06-3.61-3.09-3.33-5.84h1.42a5.69 5.69 0 003.34 4.9 8.73 8.73 0 007.58-.2 3.41 3.41 0 002-3.35 3.09 3.09 0 00-2.08-3.09c-1.56-.58-3.22-.9-4.81-1.41a35.25 35.25 0 01-4.84-1.77c-2.56-1.25-2.72-6.12.18-7.66a10.21 10.21 0 019.76-.15 5.15 5.15 0 012.79 5.55zM38.1 70.51a2.29 2.29 0 01-2.84-1.08c-1.63-2.44-3.43-4.77-5.16-7.15l-.75-1c-2.06 2.76-4.12 5.41-6 8.16a2.2 2.2 0 01-2.7 1.06l7.73-10.37-7.19-9.37a2.39 2.39 0 012.85 1c1.67 2.44 3.52 4.77 5.36 7.24 1.85-2.45 3.68-4.79 5.39-7.21a2.15 2.15 0 012.68-1l-2.79 3.7c-1.25 1.65-2.48 3.31-3.78 4.92a1 1 0 000 1.49c2.39 3.17 4.76 6.35 7.2 9.61zM70.92 50.66v1.4a7.25 7.25 0 00-7.72 7.49v11h-1.43V50.74h1.4v4.06c1.73-2.96 4.4-4.06 7.75-4.14zM2.13 60c.21-1 .34-2.09.63-3.11 1.73-6.15 8.78-8.71 13.63-4.9 2.84 2.23 3.55 5.39 3.41 8.95h-16c-.26 6.36 4.33 10.2 10.2 8.24a6.09 6.09 0 003.87-4.31c.31-1 .81-1.17 1.76-.88a8.12 8.12 0 01-3.88 5.93 9.4 9.4 0 01-10.95-1.4 9.85 9.85 0 01-2.46-5.78c0-.34-.13-.68-.2-1q-.01-.89-.01-1.74zm1.69-.43h14.47c-.09-4.61-3-7.88-6.88-7.91-4.32-.06-7.41 3.14-7.6 7.89z"></path>
      </svg>
    );
  }
  try {
    return <StackIcon name={icon} className="w-16 h-16" />;
  } catch (error) {
    console.error(`Error rendering icon ${icon}:`, error);

    return <div className="text-[#45d6e9] text-2xl">{icon}</div>;
  }
};

export default SkillPage;
