import React from "react";

interface ButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  className?: string;
  href?: string;
  colors?: string[];
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  className = "",
  href,
  colors = ["#45d6e9", "#0f92a9", "#45d6e9"],
}) => {
  const Component = href ? "a" : "button";

  return (
    <div className="relative inline-flex items-center justify-center group">
      {/* Gradient background blur effect */}
      <div
        className="absolute inset-0 opacity-50 blur-xl transition-all duration-500 group-hover:opacity-70"
        style={{
          background: `linear-gradient(90deg, ${colors.join(", ")})`,
        }}
      />

      {/* Main button/link */}
      <Component
        href={href}
        onClick={onClick}
        className={`
          relative z-10
          inline-flex items-center justify-center gap-2
          bg-black/80
          backdrop-blur-sm
          border border-[#45d6e9]/30
          rounded-lg
          px-6 py-3
          text-[#45d6e9]
          text-sm
          font-medium
          raleway-font
          transition-all
          duration-300
          hover:bg-black/60
          hover:border-[#45d6e9]/50
          hover:text-[#45d6e9]
          hover:-translate-y-0.5
          ${className}
        `}
      >
        {children}

        {/* Arrow icon */}
        <svg
          className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
          viewBox="0 0 16 16"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M1 8h12" />
          <path d="M9 3l5 5-5 5" />
        </svg>
      </Component>

      {/* Subtle dot pattern overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:10px_10px] opacity-[0.1] rounded-lg" />
    </div>
  );
};

export default Button;
