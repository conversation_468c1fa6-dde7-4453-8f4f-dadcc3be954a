"use client";
import React from "react";
import Image from "next/image";
import SpotlightCard from "../ui/SpotlightCard";

const projects = [
  {
    title: "Chatify",
    description:
      "Chatify is a modern chat application designed to provide seamless communication with an intuitive and visually appealing user interface. The app is built with a focus on functionality, usability, and extendability, making it perfect for real-time conversations",
    technologies: [
      "React",
      "Node.js",
      "MongoDB",
      "Express",
      "Socket.io",
      "Redux",
      "Vite",
      "TypeScript",
      "javascript",
      "tailwind css",
    ],
    image: "/asset/chatify.png",
    demoLink: "https://chatify-frontend-eosin.vercel.app/register",
    githubLink: "https://github.com/itz-rajkeshav/chatify__frontend",
  },
  {
    title: "CamouflageJS",
    description:
      "CamouflageJS Frontend is a user-friendly web interface designed to work with the backend API for obfuscating JavaScript code. This project provides a seamless way to interact with the obfuscation API, offering developers an intuitive experience to secure their JavaScript code.",
    technologies: [
      "React",
      "Node.js",
      "MongoDB",
      "JavaScript",
      "Express",
      "Vite",
      "Tailwind CSS",
      "TypeScript",
    ],
    image: "/asset/camaflouge.png",
    demoLink: "https://camouflage-js.vercel.app/",
    githubLink: "https://github.com/itz-rajkeshav/CamouflageJS-Server",
  },
  {
    title: "Tg_TorrentBot",
    description:
      "Tg_TorrentBot is a user-friendly Telegram bot designed to provide real-time torrent search, instant video streaming, and personalized recommendations, offering users a seamless and intuitive media experience.",
    technologies: [
      "JavaScript",
      "MongoDB",
      "node-telegram-bot-api",
      "cheerio",
      "Node.js",
      "Express",
    ],
    image: "/asset/tg_torrentBot.png",
    githubLink: "https://github.com/itz-rajkeshav/tg_TorrentBot",
  },
];

// Helper function to get technology icons
const getTechIcon = (tech: string) => {
  const iconSize = 32; // 8 * 4 = 32px (w-8 h-8)

  // Helper function to create tech icon with Next.js Image
  const createTechIcon = (src: string, alt: string, label: string) => (
    <div className="flex flex-col items-center">
      <div className="relative w-8 h-8 mb-1">
        <Image
          src={src}
          alt={alt}
          width={iconSize}
          height={iconSize}
          className="text-[#45d6e9]"
        />
      </div>
      <span className="text-xs text-gray-400">{label}</span>
    </div>
  );

  switch (tech.toLowerCase()) {
    case "react":
    case "react.js":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg",
        "React",
        "React.js"
      );
    case "node.js":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg",
        "Node.js",
        "Node.js"
      );
    case "mongodb":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg",
        "MongoDB",
        "MongoDB"
      );
    case "express":
    case "express.js":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/express/express-original.svg",
        "Express",
        "Express.js"
      );
    case "socket.io":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/socketio/socketio-original.svg",
        "Socket.IO",
        "Socket.io"
      );
    case "redux":
    case "redux.js":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/redux/redux-original.svg",
        "Redux",
        "Redux.js"
      );
    case "typescript":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg",
        "TypeScript",
        "TypeScript"
      );
    case "javascript":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg",
        "JavaScript",
        "JavaScript"
      );
    case "python":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg",
        "Python",
        "Python"
      );
    case "docker":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg",
        "Docker",
        "Docker"
      );
    case "vite":
      return createTechIcon("https://vitejs.dev/logo.svg", "Vite", "Vite");
    case "aws":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg",
        "AWS",
        "AWS"
      );
    case "tailwind css":
      return createTechIcon(
        "https://upload.wikimedia.org/wikipedia/commons/d/d5/Tailwind_CSS_Logo.svg",
        "Tailwind CSS",
        "Tailwind CSS"
      );
    case "web development":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg",
        "Web Development",
        "Web Development"
      );
    case "node-telegram-bot-api":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/telegram.svg",
        "Telegram Bot API",
        "Telegram Bot API"
      );
    case "cheerio":
      return createTechIcon(
        "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/jquery/jquery-original.svg",
        "Cheerio",
        "Cheerio"
      );
    default:
      return (
        <div className="flex flex-col items-center">
          <span className="text-sm text-[#45d6e9]">{tech}</span>
        </div>
      );
  }
};

const ProjectCard = ({ project }: { project: (typeof projects)[0] }) => {
  return (
    <SpotlightCard className="h-full">
      {/* Project Image Container */}
      <div className="relative w-full h-[200px] rounded-t-xl overflow-hidden">
        <Image
          src={project.image}
          alt={project.title}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover transition-transform duration-500 ease-in-out hover:scale-110"
          priority
        />
      </div>

      {/* Content */}
      <div className="p-6 space-y-4">
        <h3 className="text-[#45d6e9] text-xl font-bold custom">
          {project.title}
        </h3>
        <p className="text-gray-400 text-sm leading-relaxed raleway-font">
          {project.description}
        </p>

        {/* Technologies */}
        <div className="pt-4">
          <p className="text-sm text-[#45d6e9] mb-2 custom">
            Technologies Used:
          </p>
          <div className="flex flex-wrap gap-3 raleway-font">
            {project.technologies.map((tech, index) => (
              <div key={index}>{getTechIcon(tech)}</div>
            ))}
          </div>
        </div>

        {/* Buttons */}
        <div className="flex gap-3 pt-6 custom">
          {project.demoLink && (
            <a
              href={project.demoLink}
              target="_blank"
              rel="noopener noreferrer"
              className="px-6 py-2 rounded-full bg-[#45d6e9] text-black font-medium text-sm hover:bg-[#45d6e9]/90 transition-colors"
            >
              Live Demo
            </a>
          )}
          <a
            href={project.githubLink}
            target="_blank"
            rel="noopener noreferrer"
            className="px-6 py-2 rounded-full bg-black/60 text-[#45d6e9] border border-[#45d6e9]/30 text-sm hover:bg-black/80 transition-colors"
          >
            GitHub
          </a>
        </div>
      </div>
    </SpotlightCard>
  );
};

const ThirdPage = () => {
  return (
    <div
      id="projects"
      className="min-h-screen relative overflow-hidden animated-gradient-bg"
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50 z-[1]"></div>

      {/* Background patterns */}
      <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] z-[1]"></div>
      <div className="absolute inset-0 bg-[conic-gradient(at_top_left,#45d6e9_10%,transparent_30%,transparent_70%,#45d6e9_90%)] opacity-[0.15] [background-size:30px_30px] scale-[1.5] mix-blend-overlay z-[1]"></div>

      <div className="max-w-7xl mx-auto px-4 py-20 relative z-[2]">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-[#45d6e9] mb-4 custom">
            My Projects
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto ">
            A showcase of my full-stack projects, built using modern web
            technologies and frameworks.
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 px-4">
          {projects.map((project, index) => (
            <ProjectCard key={index} project={project} />
          ))}
        </div>
      </div>

      {/* Dot pattern overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] z-[1]"></div>
    </div>
  );
};

export default ThirdPage;
