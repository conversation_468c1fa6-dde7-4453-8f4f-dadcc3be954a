"use client";
import React, { useRef } from "react";
import { LinkedinIcon } from "../ui/linkedin";
import { GithubIcon } from "../ui/github";
import { MailCheckIcon as EmailIcon } from "../ui/mail-check";
import SpotlightCard from "../ui/SpotlightCard";
import { InstagramIcon } from "../ui/instagram";
//  jnmn
const Contact = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const linkedinRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const githubRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const emailRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const instagramRef = useRef<any>(null);
  const email = "<EMAIL>";
  const mailtoLink = `mailto:${email}`;

  const handleEmailClick = (e: React.MouseEvent) => {
    e.preventDefault();
    window.open(mailtoLink);
  };

  return (
    <div
      id="contact"
      className="min-h-2/12 relative overflow-hidden animated-gradient-bg"
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50 z-[1]"></div>

      {/* Background patterns */}
      <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] z-[1]"></div>
      <div className="absolute inset-0 bg-[conic-gradient(at_top_left,#45d6e9_10%,transparent_30%,transparent_70%,#45d6e9_90%)] opacity-[0.15] [background-size:30px_30px] scale-[1.5] mix-blend-overlay z-[1]"></div>

      {/* Content */}
      <div className="relative z-[2] container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#45d6e9] mb-4 custom">
            Let&apos;s Work Together!
          </h1>
          <p className="text-gray-400 text-lg md:text-xl max-w-2xl mx-auto raleway-font">
            Got an idea, opportunity, or question? I&apos;m just a message away!
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {/* Email Card */}
          <a href={mailtoLink} onClick={handleEmailClick}>
            <SpotlightCard className="transform transition-all duration-300 hover:scale-105 cursor-pointer">
              <div
                className="p-8 flex flex-col items-center justify-center group"
                onMouseEnter={() => emailRef.current?.startAnimation()}
                onMouseLeave={() => emailRef.current?.stopAnimation()}
              >
                <div className="w-16 h-16 text-[#45d6e9] mb-4 transition-colors duration-300 group-hover:text-white">
                  <EmailIcon ref={emailRef} className="w-full h-full" />
                </div>
                <h3 className="text-xl font-semibold text-[#45d6e9] mb-2 custom transition-colors duration-300 group-hover:text-white">
                  Email
                </h3>
                <p className="text-gray-400 text-sm text-center break-all raleway-font transition-colors duration-300 group-hover:text-gray-200">
                  {email}
                </p>
              </div>
            </SpotlightCard>
          </a>

          {/* LinkedIn Card */}
          <a
            href="https://linkedin.com/in/its-keshavraj"
            target="_blank"
            rel="noopener noreferrer"
          >
            <SpotlightCard className="transform transition-all duration-300 hover:scale-105">
              <div
                className="p-8 flex flex-col items-center justify-center group"
                onMouseEnter={() => linkedinRef.current?.startAnimation()}
                onMouseLeave={() => linkedinRef.current?.stopAnimation()}
              >
                <div className="w-16 h-16 text-[#45d6e9] mb-4 transition-colors duration-300 group-hover:text-white">
                  <LinkedinIcon ref={linkedinRef} className="w-full h-full" />
                </div>
                <h3 className="text-xl font-semibold text-[#45d6e9] mb-2 custom transition-colors duration-300 group-hover:text-white">
                  LinkedIn
                </h3>
                <p className="text-gray-400 text-sm text-center raleway-font transition-colors duration-300 group-hover:text-gray-200">
                  @its-keshavraj
                </p>
              </div>
            </SpotlightCard>
          </a>

          {/* GitHub Card */}
          <a
            href="https://github.com/itz-rajkeshav"
            target="_blank"
            rel="noopener noreferrer"
          >
            <SpotlightCard className="transform transition-all duration-300 hover:scale-105">
              <div
                className="p-8 flex flex-col items-center justify-center group"
                onMouseEnter={() => githubRef.current?.startAnimation()}
                onMouseLeave={() => githubRef.current?.stopAnimation()}
              >
                <div className="w-16 h-16 text-[#45d6e9] mb-4 transition-colors duration-300 group-hover:text-white">
                  <GithubIcon ref={githubRef} className="w-full h-full" />
                </div>
                <h3 className="text-xl font-semibold text-[#45d6e9] mb-2 custom transition-colors duration-300 group-hover:text-white">
                  GitHub
                </h3>
                <p className="text-gray-400 text-sm text-center raleway-font transition-colors duration-300 group-hover:text-gray-200">
                  @itz-rajkeshav
                </p>
              </div>
            </SpotlightCard>
          </a>

          {/* Instagram Card */}
          <a
            href="https://www.instagram.com/user.bot__/"
            target="_blank"
            rel="noopener noreferrer"
          >
            <SpotlightCard className="transform transition-all duration-300 hover:scale-105">
              <div
                className="p-8 flex flex-col items-center justify-center group"
                onMouseEnter={() => instagramRef.current?.startAnimation()}
                onMouseLeave={() => instagramRef.current?.stopAnimation()}
              >
                <div className="w-16 h-16 text-[#45d6e9] mb-4 transition-colors duration-300 group-hover:text-white">
                  <InstagramIcon ref={instagramRef} className="w-full h-full" />
                </div>
                <h3 className="text-xl font-semibold text-[#45d6e9] mb-2 custom transition-colors duration-300 group-hover:text-white">
                  Instagram
                </h3>
                <p className="text-gray-400 text-sm text-center raleway-font transition-colors duration-300 group-hover:text-gray-200">
                  @user.bot__
                </p>
              </div>
            </SpotlightCard>
          </a>
        </div>
      </div>
    </div>
  );
};

export default Contact;
